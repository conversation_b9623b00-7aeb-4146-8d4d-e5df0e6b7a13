class FlipDigit {
    constructor(element) {
        this.element = element;
        this.currentValue = 0;
        this.setupHTML();
    }

    setupHTML() {
        this.element.innerHTML = `
            <div class="top">0</div>
            <div class="bottom">0</div>
            <div class="top-flip">
                <div class="top-front">0</div>
                <div class="top-back">0</div>
            </div>
            <div class="bottom-flip">
                <div class="bottom-back">0</div>
                <div class="bottom-front">0</div>
            </div>
        `;
        
        this.top = this.element.querySelector('.top');
        this.bottom = this.element.querySelector('.bottom');
        this.topFlip = this.element.querySelector('.top-flip');
        this.bottomFlip = this.element.querySelector('.bottom-flip');
        this.topFront = this.element.querySelector('.top-front');
        this.topBack = this.element.querySelector('.top-back');
        this.bottomFront = this.element.querySelector('.bottom-front');
        this.bottomBack = this.element.querySelector('.bottom-back');
    }

    flipTo(value) {
        value = parseInt(value);
        if (value === this.currentValue) return;
        
        // 添加翻页动画类
        this.topFlip.classList.add('flip-top');
        this.bottomFlip.classList.add('flip-bottom');
        
        // 设置背面数字
        this.topBack.textContent = value;
        this.bottomFront.textContent = value;
        
        // 动画结束后更新顶部和底部显示
        setTimeout(() => {
            this.top.textContent = value;
            this.bottom.textContent = value;
            
            // 移除动画类
            this.topFlip.classList.remove('flip-top');
            this.bottomFlip.classList.remove('flip-bottom');
            
            this.currentValue = value;
        }, 300);
    }
}

class FlipClock {
    constructor() {
        this.digits = {
            hours: {
                tens: new FlipDigit(document.querySelector('.hours .tens')),
                units: new FlipDigit(document.querySelector('.hours .units'))
            },
            minutes: {
                tens: new FlipDigit(document.querySelector('.minutes .tens')),
                units: new FlipDigit(document.querySelector('.minutes .units'))
            },
            seconds: {
                tens: new FlipDigit(document.querySelector('.seconds .tens')),
                units: new FlipDigit(document.querySelector('.seconds .units'))
            }
        };
        
        this.dateElements = {
            year: document.querySelector('.year'),
            month: document.querySelector('.month'),
            day: document.querySelector('.day')
        };
        
        this.update();
        setInterval(() => this.update(), 1000);
    }
    
    update() {
        const now = new Date();
        
        // 更新时间
        this.updateTimePart('hours', now.getHours());
        this.updateTimePart('minutes', now.getMinutes());
        this.updateTimePart('seconds', now.getSeconds());
        
        // 更新日期
        this.dateElements.year.textContent = now.getFullYear();
        this.dateElements.month.textContent = (now.getMonth() + 1).toString().padStart(2, '0');
        this.dateElements.day.textContent = now.getDate().toString().padStart(2, '0');
    }
    
    updateTimePart(part, value) {
        const tens = Math.floor(value / 10);
        const units = value % 10;
        
        this.digits[part].tens.flipTo(tens);
        this.digits[part].units.flipTo(units);
    }
}

// 初始化时钟
document.addEventListener('DOMContentLoaded', () => {
    new FlipClock();
});