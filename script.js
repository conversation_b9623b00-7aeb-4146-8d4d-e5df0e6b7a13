class FlipDigit {
    constructor(element) {
        this.element = element;
        this.currentValue = 0;
        this.setupHTML();
    }

    setupHTML() {
        this.element.innerHTML = `
            <div class="digit-display">0</div>
        `;

        this.display = this.element.querySelector('.digit-display');
    }

    updateTo(value) {
        value = parseInt(value);
        if (value === this.currentValue) return;

        // 添加更新动画
        this.display.classList.add('digit-update');

        // 更新数字
        this.display.textContent = value;
        this.currentValue = value;

        // 移除动画类
        setTimeout(() => {
            this.display.classList.remove('digit-update');
        }, 300);
    }
}

class FlipClock {
    constructor() {
        this.digits = {
            hours: {
                tens: new FlipDigit(document.querySelector('.hours .tens')),
                units: new FlipDigit(document.querySelector('.hours .units'))
            },
            minutes: {
                tens: new FlipDigit(document.querySelector('.minutes .tens')),
                units: new FlipDigit(document.querySelector('.minutes .units'))
            },
            seconds: {
                tens: new FlipDigit(document.querySelector('.seconds .tens')),
                units: new FlipDigit(document.querySelector('.seconds .units'))
            }
        };
        
        this.dateElements = {
            year: document.querySelector('.year'),
            month: document.querySelector('.month'),
            day: document.querySelector('.day')
        };
        
        this.update();
        setInterval(() => this.update(), 1000);
    }
    
    update() {
        const now = new Date();

        // 更新时间 - 确保24小时制格式
        const hours = now.getHours();
        const minutes = now.getMinutes();
        const seconds = now.getSeconds();

        this.updateTimePart('hours', hours);
        this.updateTimePart('minutes', minutes);
        this.updateTimePart('seconds', seconds);

        // 更新日期 - 确保正确的格式
        this.dateElements.year.textContent = now.getFullYear();
        this.dateElements.month.textContent = String(now.getMonth() + 1).padStart(2, '0');
        this.dateElements.day.textContent = String(now.getDate()).padStart(2, '0');
    }
    
    updateTimePart(part, value) {
        // 确保值是两位数格式
        const paddedValue = String(value).padStart(2, '0');
        const tens = parseInt(paddedValue[0]);
        const units = parseInt(paddedValue[1]);

        this.digits[part].tens.updateTo(tens);
        this.digits[part].units.updateTo(units);
    }
}

// 初始化时钟
document.addEventListener('DOMContentLoaded', () => {
    new FlipClock();
});