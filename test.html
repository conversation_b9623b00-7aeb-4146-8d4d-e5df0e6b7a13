<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=640, height=480, initial-scale=1.0, user-scalable=no">
    <title>时钟测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #000;
            color: #fff;
            padding: 20px;
            font-size: 24px;
        }
        .test-info {
            margin-bottom: 20px;
        }
        .current-time {
            font-size: 32px;
            color: #64b5f6;
        }
    </style>
</head>
<body>
    <div class="test-info">
        <h1>时钟格式测试</h1>
        <div class="current-time" id="currentTime"></div>
        <div id="formatTest"></div>
    </div>
    
    <script>
        function updateTest() {
            const now = new Date();
            const hours = now.getHours();
            const minutes = now.getMinutes();
            const seconds = now.getSeconds();
            
            // 显示当前时间
            document.getElementById('currentTime').textContent = 
                `当前时间: ${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
            
            // 测试格式化
            const hoursFormatted = String(hours).padStart(2, '0');
            const minutesFormatted = String(minutes).padStart(2, '0');
            const secondsFormatted = String(seconds).padStart(2, '0');
            
            document.getElementById('formatTest').innerHTML = `
                <p>小时: ${hours} -> ${hoursFormatted} (十位: ${hoursFormatted[0]}, 个位: ${hoursFormatted[1]})</p>
                <p>分钟: ${minutes} -> ${minutesFormatted} (十位: ${minutesFormatted[0]}, 个位: ${minutesFormatted[1]})</p>
                <p>秒钟: ${seconds} -> ${secondsFormatted} (十位: ${secondsFormatted[0]}, 个位: ${secondsFormatted[1]})</p>
                <p>日期: ${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')}</p>
            `;
        }
        
        updateTest();
        setInterval(updateTest, 1000);
    </script>
</body>
</html>
