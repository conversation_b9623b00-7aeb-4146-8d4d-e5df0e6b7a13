<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>布局测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #000;
            color: #fff;
            padding: 20px;
            margin: 0;
        }
        .test-container {
            display: flex;
            flex-direction: column;
            gap: 30px;
        }
        .test-frame {
            border: 2px solid #64b5f6;
            border-radius: 10px;
            overflow: hidden;
            position: relative;
        }
        .test-label {
            position: absolute;
            top: -15px;
            left: 10px;
            background: #000;
            padding: 0 10px;
            font-size: 14px;
            color: #64b5f6;
        }
        .frame-large {
            width: 800px;
            height: 600px;
        }
        .frame-640 {
            width: 640px;
            height: 480px;
        }
        .frame-small {
            width: 480px;
            height: 320px;
        }
        iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
    </style>
</head>
<body>
    <h1>翻页时钟 - 不同屏幕尺寸效果测试</h1>
    
    <div class="test-container">
        <div class="test-frame frame-large">
            <div class="test-label">大屏幕 (800x600)</div>
            <iframe src="index.html"></iframe>
        </div>
        
        <div class="test-frame frame-640">
            <div class="test-label">640x480 屏幕</div>
            <iframe src="index.html"></iframe>
        </div>
        
        <div class="test-frame frame-small">
            <div class="test-label">小屏幕 (480x320)</div>
            <iframe src="index.html"></iframe>
        </div>
    </div>
</body>
</html>
