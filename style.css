* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #0f0f23, #1a1a2e, #16213e);
    background-size: 400% 400%;
    animation: gradientShift 8s ease infinite;
    height: 100vh;
    width: 100vw;
    min-height: 480px;
    min-width: 640px;
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0;
    padding: 0;
}

.clock-container {
    text-align: center;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.date-time-display {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.date-display {
    display: flex;
    justify-content: center;
    align-items: baseline;
    gap: 8px;
    margin-bottom: 25px;
    color: #fff;
    font-size: 32px;
    font-weight: bold;
    text-shadow: 2px 2px 8px rgba(0,0,0,0.7);
    letter-spacing: 2px;
}

.date-part {
    color: #fff;
    font-size: 32px;
    font-weight: bold;
    background: linear-gradient(45deg, #64b5f6, #42a5f5);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: none;
}

.date-separator {
    color: #90caf9;
    font-size: 28px;
    opacity: 0.8;
    font-weight: bold;
}

.time-display {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 12px;
    padding: 0 20px;
}

.separator {
    color: #64b5f6;
    font-size: 72px;
    font-weight: bold;
    animation: blink 1s step-end infinite;
    text-shadow: 0 0 20px rgba(100, 181, 246, 0.6), 2px 2px 8px rgba(0,0,0,0.5);
    opacity: 0.9;
    line-height: 1;
}

.time-part {
    display: flex;
}

.digit {
    position: relative;
    width: 80px;
    height: 120px;
    margin: 0 4px;
    background: linear-gradient(145deg, #2c2c54, #1a1a2e);
    border-radius: 15px;
    overflow: hidden;
    box-shadow:
        0 8px 16px rgba(0,0,0,0.6),
        inset 0 1px 0 rgba(255,255,255,0.1),
        inset 0 -1px 0 rgba(0,0,0,0.3);
    border: 2px solid #40407a;
}

.digit::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 50%;
    background: linear-gradient(to bottom, rgba(255,255,255,0.15), transparent);
    pointer-events: none;
}

.digit::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: #666;
    box-shadow: 0 -2px 3px rgba(0,0,0,0.6);
    pointer-events: none;
}

.digit .top,
.digit .bottom,
.digit .top-flip,
.digit .bottom-flip {
    position: absolute;
    left: 0;
    width: 100%;
    height: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 76px;
    font-weight: bold;
    color: #fff;
    text-shadow:
        0 0 10px rgba(100, 181, 246, 0.4),
        2px 2px 8px rgba(0,0,0,0.8);
    overflow: hidden;
    font-family: 'Arial', monospace;
    line-height: 1;
}

.digit .top {
    top: 0;
    border-bottom: 2px solid #40407a;
    background: linear-gradient(180deg, #2c2c54 0%, #1a1a2e 100%);
}

.digit .bottom {
    bottom: 0;
    border-top: 2px solid #40407a;
    background: linear-gradient(0deg, #2c2c54 0%, #1a1a2e 100%);
}

.digit .top-flip,
.digit .bottom-flip {
    background: linear-gradient(145deg, #2c2c54, #1a1a2e);
}

.digit .top-flip {
    top: 0;
    transform-origin: top center;
}

.digit .bottom-flip {
    bottom: 0;
    transform-origin: bottom center;
}

@keyframes blink {
    0%, 49% { opacity: 1; }
    50%, 100% { opacity: 0; }
}

/* 翻页动画 */
.flip-top {
    animation: flipTop 0.4s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.flip-bottom {
    animation: flipBottom 0.4s cubic-bezier(0.4, 0.0, 0.2, 1);
}

@keyframes flipTop {
    0% { transform: rotateX(0deg); }
    100% { transform: rotateX(-90deg); }
}

@keyframes flipBottom {
    0% { transform: rotateX(90deg); }
    100% { transform: rotateX(0deg); }
}

/* 添加金属质感边框效果 */
.digit {
    position: relative;
}

.digit::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    right: 2px;
    bottom: 2px;
    border-radius: 8px;
    background: linear-gradient(135deg, rgba(255,255,255,0.1), rgba(0,0,0,0.3));
    pointer-events: none;
    z-index: 2;
}

/* 日期时间整体容器 */
.date-time-display {
    padding: 40px 50px;
    border-radius: 25px;
    background: rgba(0,0,0,0.3);
    backdrop-filter: blur(15px);
    border: 2px solid rgba(100, 181, 246, 0.2);
    box-shadow:
        0 12px 40px rgba(0,0,0,0.4),
        inset 0 1px 0 rgba(255,255,255,0.1);
    position: relative;
    overflow: hidden;
}

.date-time-display::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg,
        rgba(100, 181, 246, 0.05) 0%,
        transparent 50%,
        rgba(100, 181, 246, 0.05) 100%);
    pointer-events: none;
}

/* 背景动画 */
@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* 640x480 优化 */
@media screen and (max-width: 640px), screen and (max-height: 480px) {
    .date-time-display {
        padding: 30px 40px;
        transform: scale(0.9);
    }

    .date-display {
        font-size: 28px;
        margin-bottom: 20px;
    }

    .date-part {
        font-size: 28px;
    }

    .date-separator {
        font-size: 24px;
    }

    .separator {
        font-size: 64px;
    }

    .digit {
        width: 70px;
        height: 105px;
        margin: 0 3px;
    }

    .digit .top,
    .digit .bottom,
    .digit .top-flip,
    .digit .bottom-flip {
        font-size: 68px;
    }

    .time-display {
        gap: 10px;
        padding: 0 15px;
    }
}

/* 确保全屏显示 */
html {
    height: 100%;
    margin: 0;
    padding: 0;
}

body {
    cursor: none; /* 隐藏鼠标指针，适合全屏时钟 */
}