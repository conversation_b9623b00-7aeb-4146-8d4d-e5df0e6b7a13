* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #0f0f23, #1a1a2e, #16213e);
    background-size: 400% 400%;
    animation: gradientShift 8s ease infinite;
    height: 100vh;
    width: 100vw;
    min-height: 480px;
    min-width: 640px;
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0;
    padding: 0;
}

.clock-container {
    text-align: center;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.date-time-display {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.date-display {
    display: flex;
    justify-content: center;
    align-items: baseline;
    gap: 8px;
    margin-bottom: 30px;
    color: #fff;
    font-size: 38px;
    font-weight: bold;
    text-shadow: 2px 2px 8px rgba(0,0,0,0.7);
    letter-spacing: 2px;
}

.date-part {
    color: #fff;
    font-size: 38px;
    font-weight: bold;
    background: linear-gradient(45deg, #64b5f6, #42a5f5);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: none;
}

.date-separator {
    color: #90caf9;
    font-size: 34px;
    opacity: 0.8;
    font-weight: bold;
}

.time-display {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 15px;
    padding: 0 25px;
}

.separator {
    color: #64b5f6;
    font-size: 100px;
    font-weight: bold;
    animation: blink 1s step-end infinite;
    text-shadow: 0 0 20px rgba(100, 181, 246, 0.6), 2px 2px 8px rgba(0,0,0,0.5);
    opacity: 0.9;
    line-height: 1;
    display: flex;
    align-items: center;
}

/* 最后一个分隔符（秒钟前的）稍小一些 */
.separator:last-of-type {
    font-size: 80px;
    opacity: 0.7;
}

.time-part {
    display: flex;
}

.digit {
    position: relative;
    background: linear-gradient(145deg, #2c2c54, #1a1a2e);
    border-radius: 15px;
    overflow: hidden;
    box-shadow:
        0 8px 16px rgba(0,0,0,0.6),
        inset 0 1px 0 rgba(255,255,255,0.1),
        inset 0 -1px 0 rgba(0,0,0,0.3);
    border: 2px solid #40407a;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 小时和分钟 - 大尺寸 */
.hours .digit,
.minutes .digit {
    width: 110px;
    height: 160px;
    margin: 0 5px;
}

/* 秒钟 - 小尺寸 */
.seconds .digit {
    width: 80px;
    height: 120px;
    margin: 0 4px;
}

.digit::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 50%;
    background: linear-gradient(to bottom, rgba(255,255,255,0.15), transparent);
    pointer-events: none;
    z-index: 1;
}

.digit-display {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    color: #fff;
    text-shadow:
        0 0 15px rgba(100, 181, 246, 0.6),
        2px 2px 8px rgba(0,0,0,0.8);
    font-family: 'Arial', monospace;
    line-height: 1;
    transition: all 0.3s ease;
}

/* 小时和分钟的数字大小 */
.hours .digit-display,
.minutes .digit-display {
    font-size: 100px;
}

/* 秒钟的数字大小和样式 */
.seconds .digit-display {
    font-size: 75px;
    opacity: 0.8;
}

.seconds .digit {
    opacity: 0.9;
}

/* 数字更新动画 */
.digit-update {
    transform: scale(1.1);
    text-shadow:
        0 0 25px rgba(100, 181, 246, 0.8),
        2px 2px 8px rgba(0,0,0,0.8);
}

/* 移除翻页相关样式 */

@keyframes blink {
    0%, 49% { opacity: 1; }
    50%, 100% { opacity: 0; }
}

/* 数字更新动画 */
@keyframes digitPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* 移除重复的样式定义 */

/* 日期时间整体容器 */
.date-time-display {
    padding: 35px 45px;
    border-radius: 25px;
    background: rgba(0,0,0,0.3);
    backdrop-filter: blur(15px);
    border: 2px solid rgba(100, 181, 246, 0.2);
    box-shadow:
        0 12px 40px rgba(0,0,0,0.4),
        inset 0 1px 0 rgba(255,255,255,0.1);
    position: relative;
    overflow: hidden;
    max-width: 90vw;
    max-height: 85vh;
}

.date-time-display::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg,
        rgba(100, 181, 246, 0.05) 0%,
        transparent 50%,
        rgba(100, 181, 246, 0.05) 100%);
    pointer-events: none;
}

/* 背景动画 */
@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* 640x480 优化 */
@media screen and (max-width: 640px), screen and (max-height: 480px) {
    .date-time-display {
        padding: 25px 35px;
        max-width: 95vw;
        max-height: 90vh;
    }

    .date-display {
        font-size: 32px;
        margin-bottom: 20px;
        gap: 8px;
    }

    .date-part {
        font-size: 32px;
    }

    .date-separator {
        font-size: 28px;
    }

    .separator {
        font-size: 85px;
    }

    .separator:last-of-type {
        font-size: 70px;
    }

    .hours .digit,
    .minutes .digit {
        width: 95px;
        height: 140px;
        margin: 0 3px;
    }

    .seconds .digit {
        width: 70px;
        height: 105px;
        margin: 0 3px;
    }

    .hours .digit-display,
    .minutes .digit-display {
        font-size: 85px;
    }

    .seconds .digit-display {
        font-size: 65px;
    }

    .time-display {
        gap: 12px;
        padding: 0 15px;
    }
}

/* 确保全屏显示 */
html {
    height: 100%;
    margin: 0;
    padding: 0;
}

body {
    cursor: none; /* 隐藏鼠标指针，适合全屏时钟 */
}

/* 专门针对640x480分辨率的优化 - 更大尺寸 */
@media screen and (width: 640px) and (height: 480px) {
    .date-time-display {
        padding: 20px 30px;
        max-width: 620px;
        max-height: 460px;
    }

    .date-display {
        font-size: 30px;
        margin-bottom: 18px;
        gap: 8px;
    }

    .date-part {
        font-size: 30px;
    }

    .date-separator {
        font-size: 26px;
    }

    .separator {
        font-size: 80px;
    }

    .separator:last-of-type {
        font-size: 65px;
    }

    .hours .digit,
    .minutes .digit {
        width: 85px;
        height: 125px;
        margin: 0 3px;
    }

    .seconds .digit {
        width: 60px;
        height: 90px;
        margin: 0 2px;
    }

    .hours .digit-display,
    .minutes .digit-display {
        font-size: 75px;
    }

    .seconds .digit-display {
        font-size: 55px;
    }

    .time-display {
        gap: 10px;
        padding: 0 5px;
    }
}