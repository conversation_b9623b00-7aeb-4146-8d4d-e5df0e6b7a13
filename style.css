* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #1e3c72, #2a5298);
    height: 480px;
    width: 640px;
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
}

.clock-container {
    text-align: center;
}

.date-time-display {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.date-display {
    display: flex;
    justify-content: center;
    align-items: baseline;
    gap: 4px;
    margin-bottom: 15px;
    color: #fff;
    font-size: 28px;
    font-weight: bold;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.date-part {
    color: #fff;
    font-size: 28px;
    font-weight: bold;
}

.date-separator {
    color: #fff;
    font-size: 24px;
    opacity: 0.7;
}

.time-display {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
}

.separator {
    color: #fff;
    font-size: 64px;
    font-weight: bold;
    animation: blink 1s step-end infinite;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
    opacity: 0.8;
}

.time-part {
    display: flex;
}

.digit {
    position: relative;
    width: 70px;
    height: 110px;
    margin: 0 3px;
    background: #333;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 6px 12px rgba(0,0,0,0.4);
    border: 2px solid #444;
}

.digit::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 50%;
    background: linear-gradient(to bottom, rgba(255,255,255,0.15), transparent);
    pointer-events: none;
}

.digit::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: #666;
    box-shadow: 0 -2px 3px rgba(0,0,0,0.6);
    pointer-events: none;
}

.digit .top,
.digit .bottom,
.digit .top-flip,
.digit .bottom-flip {
    position: absolute;
    left: 0;
    width: 100%;
    height: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 72px;
    font-weight: bold;
    color: #fff;
    text-shadow: 3px 3px 6px rgba(0,0,0,0.6);
    overflow: hidden;
    text-transform: uppercase;
}

.digit .top {
    top: 0;
    border-bottom: 2px solid #555;
}

.digit .bottom {
    bottom: 0;
    border-top: 2px solid #555;
}

.digit .top-flip,
.digit .bottom-flip {
    background: #333;
}

.digit .top-flip {
    top: 0;
    transform-origin: top center;
}

.digit .bottom-flip {
    bottom: 0;
    transform-origin: bottom center;
}

@keyframes blink {
    0%, 49% { opacity: 1; }
    50%, 100% { opacity: 0; }
}

/* 翻页动画 */
.flip-top {
    animation: flipTop 0.4s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.flip-bottom {
    animation: flipBottom 0.4s cubic-bezier(0.4, 0.0, 0.2, 1);
}

@keyframes flipTop {
    0% { transform: rotateX(0deg); }
    100% { transform: rotateX(-90deg); }
}

@keyframes flipBottom {
    0% { transform: rotateX(90deg); }
    100% { transform: rotateX(0deg); }
}

/* 添加金属质感边框效果 */
.digit {
    position: relative;
}

.digit::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    right: 2px;
    bottom: 2px;
    border-radius: 8px;
    background: linear-gradient(135deg, rgba(255,255,255,0.1), rgba(0,0,0,0.3));
    pointer-events: none;
    z-index: 2;
}

/* 日期时间整体容器 */
.date-time-display {
    padding: 20px;
    border-radius: 20px;
    background: rgba(0,0,0,0.2);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.1);
    box-shadow: 0 8px 32px rgba(0,0,0,0.3);
}